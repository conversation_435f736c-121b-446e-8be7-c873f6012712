{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\\\u064A\\u0627\\u0646\\u0629 \\u0645\\u0648\\u0628\\u0627\\u064A\\u0644\\u0627\\u062A \\u0628\\u0631\\u0646\\u0627\\u0645\\u062C\\\\src\\\\components\\\\Layout\\\\Layout.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport './Layout.css';\nimport { Box, Drawer, AppBar, Toolbar, List, Typography, Divider, IconButton, ListItem, ListItemButton, ListItemIcon, ListItemText, Avatar, Menu, MenuItem, TextField, InputAdornment, Badge, Tooltip } from '@mui/material';\nimport { Menu as MenuIcon, Dashboard as DashboardIcon, Add as AddIcon, List as ListIcon, People as PeopleIcon, Build as BuildIcon, Description as DescriptionIcon, Assessment as AssessmentIcon, History as HistoryIcon, Settings as SettingsIcon, Logout as LogoutIcon, Search as SearchIcon, Notifications as NotificationsIcon, PhoneAndroid as PhoneIcon } from '@mui/icons-material';\nimport { useNavigate, useLocation } from 'react-router-dom';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst drawerWidth = 280;\nconst menuItems = [{\n  text: 'لوحة التحكم',\n  icon: /*#__PURE__*/_jsxDEV(DashboardIcon, {}, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 45,\n    columnNumber: 32\n  }, this),\n  path: '/dashboard'\n}, {\n  text: 'إضافة صيانة',\n  icon: /*#__PURE__*/_jsxDEV(AddIcon, {}, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 46,\n    columnNumber: 32\n  }, this),\n  path: '/add-repair'\n}, {\n  text: 'عرض الطلبات',\n  icon: /*#__PURE__*/_jsxDEV(ListIcon, {}, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 47,\n    columnNumber: 32\n  }, this),\n  path: '/repairs'\n}, {\n  text: 'العملاء',\n  icon: /*#__PURE__*/_jsxDEV(PeopleIcon, {}, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 48,\n    columnNumber: 28\n  }, this),\n  path: '/customers'\n}, {\n  text: 'قطع الغيار',\n  icon: /*#__PURE__*/_jsxDEV(BuildIcon, {}, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 49,\n    columnNumber: 31\n  }, this),\n  path: '/spare-parts'\n}, {\n  text: 'المستندات',\n  icon: /*#__PURE__*/_jsxDEV(DescriptionIcon, {}, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 50,\n    columnNumber: 30\n  }, this),\n  path: '/documents'\n}, {\n  text: 'التقارير',\n  icon: /*#__PURE__*/_jsxDEV(AssessmentIcon, {}, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 51,\n    columnNumber: 29\n  }, this),\n  path: '/reports'\n}, {\n  text: 'السجل',\n  icon: /*#__PURE__*/_jsxDEV(HistoryIcon, {}, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 52,\n    columnNumber: 26\n  }, this),\n  path: '/activity-log'\n}, {\n  text: 'الإعدادات',\n  icon: /*#__PURE__*/_jsxDEV(SettingsIcon, {}, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 53,\n    columnNumber: 30\n  }, this),\n  path: '/settings'\n}];\nconst Layout = ({\n  children,\n  onLogout\n}) => {\n  _s();\n  const [mobileOpen, setMobileOpen] = useState(false);\n  const [anchorEl, setAnchorEl] = useState(null);\n  const [searchQuery, setSearchQuery] = useState('');\n  const navigate = useNavigate();\n  const location = useLocation();\n  const handleDrawerToggle = () => {\n    setMobileOpen(!mobileOpen);\n  };\n  const handleMenuClick = event => {\n    setAnchorEl(event.currentTarget);\n  };\n  const handleMenuClose = () => {\n    setAnchorEl(null);\n  };\n  const handleLogout = () => {\n    handleMenuClose();\n    onLogout();\n  };\n  const handleSearch = e => {\n    setSearchQuery(e.target.value);\n    // يمكن إضافة منطق البحث هنا\n  };\n  const drawer = /*#__PURE__*/_jsxDEV(Box, {\n    sx: {\n      height: '100%',\n      background: 'linear-gradient(180deg, rgba(255,255,255,0.95) 0%, rgba(255,255,255,0.9) 100%)',\n      backdropFilter: 'blur(20px)'\n    },\n    children: [/*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        p: 3,\n        textAlign: 'center',\n        background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n        color: 'white'\n      },\n      children: [/*#__PURE__*/_jsxDEV(Avatar, {\n        sx: {\n          width: 60,\n          height: 60,\n          margin: '0 auto 12px',\n          background: 'rgba(255,255,255,0.2)',\n          backdropFilter: 'blur(10px)'\n        },\n        children: /*#__PURE__*/_jsxDEV(PhoneIcon, {\n          sx: {\n            fontSize: 30\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 110,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 101,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h6\",\n        sx: {\n          fontWeight: 600,\n          mb: 1\n        },\n        children: \"\\u0646\\u0638\\u0627\\u0645 \\u0625\\u062F\\u0627\\u0631\\u0629 \\u0627\\u0644\\u0635\\u064A\\u0627\\u0646\\u0629\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 112,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"body2\",\n        sx: {\n          opacity: 0.9\n        },\n        children: \"\\u0645\\u0631\\u0643\\u0632 \\u0635\\u064A\\u0627\\u0646\\u0629 \\u0627\\u0644\\u0645\\u0648\\u0628\\u0627\\u064A\\u0644\\u0627\\u062A\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 115,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 93,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(List, {\n      sx: {\n        px: 2,\n        py: 2\n      },\n      children: menuItems.map((item, index) => /*#__PURE__*/_jsxDEV(ListItem, {\n        disablePadding: true,\n        sx: {\n          mb: 1\n        },\n        children: /*#__PURE__*/_jsxDEV(ListItemButton, {\n          onClick: () => navigate(item.path),\n          selected: location.pathname === item.path,\n          sx: {\n            borderRadius: 2,\n            transition: 'all 0.3s ease',\n            '&:hover': {\n              background: 'linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%)',\n              transform: 'translateX(-4px)',\n              boxShadow: '4px 0 12px rgba(102, 126, 234, 0.2)'\n            },\n            '&.Mui-selected': {\n              background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n              color: 'white',\n              '&:hover': {\n                background: 'linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%)'\n              },\n              '& .MuiListItemIcon-root': {\n                color: 'white'\n              }\n            }\n          },\n          children: [/*#__PURE__*/_jsxDEV(ListItemIcon, {\n            sx: {\n              minWidth: 40,\n              transition: 'all 0.3s ease'\n            },\n            children: item.icon\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 146,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(ListItemText, {\n            primary: item.text,\n            sx: {\n              '& .MuiTypography-root': {\n                fontWeight: location.pathname === item.path ? 600 : 400\n              }\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 154,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 123,\n          columnNumber: 13\n        }, this)\n      }, item.text, false, {\n        fileName: _jsxFileName,\n        lineNumber: 122,\n        columnNumber: 11\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 120,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 86,\n    columnNumber: 5\n  }, this);\n  return /*#__PURE__*/_jsxDEV(Box, {\n    sx: {\n      display: 'flex'\n    },\n    children: [/*#__PURE__*/_jsxDEV(AppBar, {\n      position: \"fixed\",\n      sx: {\n        width: '100%',\n        background: 'rgba(255, 255, 255, 0.95)',\n        backdropFilter: 'blur(20px)',\n        color: 'text.primary',\n        boxShadow: '0 4px 20px rgba(0,0,0,0.1)',\n        borderBottom: '1px solid rgba(0,0,0,0.05)',\n        zIndex: 1201\n      },\n      children: /*#__PURE__*/_jsxDEV(Toolbar, {\n        children: [/*#__PURE__*/_jsxDEV(IconButton, {\n          color: \"inherit\",\n          \"aria-label\": \"open drawer\",\n          edge: \"start\",\n          onClick: handleDrawerToggle,\n          sx: {\n            mr: 2,\n            display: {\n              sm: 'none'\n            }\n          },\n          children: /*#__PURE__*/_jsxDEV(MenuIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 191,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 184,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(TextField, {\n          placeholder: \"\\u0627\\u0644\\u0628\\u062D\\u062B \\u0641\\u064A \\u0627\\u0644\\u0646\\u0638\\u0627\\u0645...\",\n          value: searchQuery,\n          onChange: handleSearch,\n          size: \"small\",\n          sx: {\n            flexGrow: 1,\n            maxWidth: 400,\n            '& .MuiOutlinedInput-root': {\n              borderRadius: 3,\n              background: 'rgba(255,255,255,0.8)',\n              transition: 'all 0.3s ease',\n              '&:hover': {\n                background: 'rgba(255,255,255,0.9)',\n                transform: 'translateY(-1px)',\n                boxShadow: '0 4px 12px rgba(0,0,0,0.1)'\n              },\n              '&.Mui-focused': {\n                background: 'white',\n                transform: 'translateY(-1px)',\n                boxShadow: '0 4px 12px rgba(102, 126, 234, 0.2)'\n              }\n            }\n          },\n          InputProps: {\n            startAdornment: /*#__PURE__*/_jsxDEV(InputAdornment, {\n              position: \"start\",\n              children: /*#__PURE__*/_jsxDEV(SearchIcon, {\n                color: \"action\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 221,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 220,\n              columnNumber: 17\n            }, this)\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 194,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            flexGrow: 1\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 227,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n          title: \"\\u0627\\u0644\\u0625\\u0634\\u0639\\u0627\\u0631\\u0627\\u062A\",\n          children: /*#__PURE__*/_jsxDEV(IconButton, {\n            color: \"inherit\",\n            sx: {\n              mr: 2,\n              transition: 'all 0.3s ease',\n              '&:hover': {\n                transform: 'scale(1.1)',\n                color: 'primary.main'\n              }\n            },\n            children: /*#__PURE__*/_jsxDEV(Badge, {\n              badgeContent: 3,\n              color: \"error\",\n              children: /*#__PURE__*/_jsxDEV(NotificationsIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 242,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 241,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 230,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 229,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n          title: \"\\u0627\\u0644\\u0645\\u0644\\u0641 \\u0627\\u0644\\u0634\\u062E\\u0635\\u064A\",\n          children: /*#__PURE__*/_jsxDEV(IconButton, {\n            onClick: handleMenuClick,\n            sx: {\n              transition: 'all 0.3s ease',\n              '&:hover': {\n                transform: 'scale(1.05)'\n              }\n            },\n            children: /*#__PURE__*/_jsxDEV(Avatar, {\n              sx: {\n                width: 40,\n                height: 40,\n                background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n                fontSize: '1rem',\n                fontWeight: 600\n              },\n              children: \"A\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 257,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 248,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 247,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Menu, {\n          anchorEl: anchorEl,\n          open: Boolean(anchorEl),\n          onClose: handleMenuClose,\n          PaperProps: {\n            sx: {\n              borderRadius: 2,\n              boxShadow: '0 8px 32px rgba(0,0,0,0.15)',\n              border: '1px solid rgba(0,0,0,0.05)'\n            }\n          },\n          children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n            onClick: () => navigate('/settings'),\n            children: [/*#__PURE__*/_jsxDEV(ListItemIcon, {\n              children: /*#__PURE__*/_jsxDEV(SettingsIcon, {\n                fontSize: \"small\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 285,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 284,\n              columnNumber: 15\n            }, this), \"\\u0627\\u0644\\u0625\\u0639\\u062F\\u0627\\u062F\\u0627\\u062A\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 283,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Divider, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 289,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n            onClick: handleLogout,\n            children: [/*#__PURE__*/_jsxDEV(ListItemIcon, {\n              children: /*#__PURE__*/_jsxDEV(LogoutIcon, {\n                fontSize: \"small\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 292,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 291,\n              columnNumber: 15\n            }, this), \"\\u062A\\u0633\\u062C\\u064A\\u0644 \\u0627\\u0644\\u062E\\u0631\\u0648\\u062C\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 290,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 271,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 183,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 171,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Box, {\n      component: \"main\",\n      sx: {\n        flexGrow: 1,\n        p: 3,\n        width: {\n          sm: `calc(100% - ${drawerWidth}px)`\n        },\n        mr: {\n          sm: 0\n        },\n        ml: {\n          sm: 0\n        },\n        marginTop: '64px',\n        background: 'linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%)',\n        minHeight: 'calc(100vh - 64px)'\n      },\n      children: /*#__PURE__*/_jsxDEV(Box, {\n        className: \"fade-in\",\n        children: children\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 313,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 300,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Box, {\n      component: \"nav\",\n      sx: {\n        width: {\n          sm: drawerWidth\n        },\n        flexShrink: {\n          sm: 0\n        }\n      },\n      children: [/*#__PURE__*/_jsxDEV(Drawer, {\n        variant: \"temporary\",\n        anchor: \"left\",\n        open: mobileOpen,\n        onClose: handleDrawerToggle,\n        ModalProps: {\n          keepMounted: true\n        },\n        sx: {\n          display: {\n            xs: 'block',\n            sm: 'none'\n          },\n          '& .MuiDrawer-paper': {\n            boxSizing: 'border-box',\n            width: drawerWidth,\n            border: 'none'\n          }\n        },\n        children: drawer\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 325,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Drawer, {\n        variant: \"permanent\",\n        anchor: \"right\",\n        sx: {\n          display: {\n            xs: 'none',\n            sm: 'block'\n          },\n          '& .MuiDrawer-paper': {\n            boxSizing: 'border-box',\n            width: drawerWidth,\n            border: 'none',\n            boxShadow: '-4px 0 20px rgba(0,0,0,0.1)'\n          }\n        },\n        open: true,\n        children: drawer\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 344,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 318,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 170,\n    columnNumber: 5\n  }, this);\n};\n_s(Layout, \"j27G3IXB/stxJQkKLKmZ2nqMO4U=\", false, function () {\n  return [useNavigate, useLocation];\n});\n_c = Layout;\nexport default Layout;\nvar _c;\n$RefreshReg$(_c, \"Layout\");", "map": {"version": 3, "names": ["React", "useState", "Box", "Drawer", "AppBar", "<PERSON><PERSON><PERSON>", "List", "Typography", "Divider", "IconButton", "ListItem", "ListItemButton", "ListItemIcon", "ListItemText", "Avatar", "<PERSON><PERSON>", "MenuItem", "TextField", "InputAdornment", "Badge", "<PERSON><PERSON><PERSON>", "MenuIcon", "Dashboard", "DashboardIcon", "Add", "AddIcon", "ListIcon", "People", "PeopleIcon", "Build", "BuildIcon", "Description", "DescriptionIcon", "Assessment", "AssessmentIcon", "History", "HistoryIcon", "Settings", "SettingsIcon", "Logout", "LogoutIcon", "Search", "SearchIcon", "Notifications", "NotificationsIcon", "PhoneAndroid", "PhoneIcon", "useNavigate", "useLocation", "jsxDEV", "_jsxDEV", "drawerWidth", "menuItems", "text", "icon", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "path", "Layout", "children", "onLogout", "_s", "mobileOpen", "setMobileOpen", "anchorEl", "setAnchorEl", "searchQuery", "setSearch<PERSON>uery", "navigate", "location", "handleDrawerToggle", "handleMenuClick", "event", "currentTarget", "handleMenuClose", "handleLogout", "handleSearch", "e", "target", "value", "drawer", "sx", "height", "background", "<PERSON><PERSON>ilter", "p", "textAlign", "color", "width", "margin", "fontSize", "variant", "fontWeight", "mb", "opacity", "px", "py", "map", "item", "index", "disablePadding", "onClick", "selected", "pathname", "borderRadius", "transition", "transform", "boxShadow", "min<PERSON><PERSON><PERSON>", "primary", "display", "position", "borderBottom", "zIndex", "edge", "mr", "sm", "placeholder", "onChange", "size", "flexGrow", "max<PERSON><PERSON><PERSON>", "InputProps", "startAdornment", "title", "badgeContent", "open", "Boolean", "onClose", "PaperProps", "border", "component", "ml", "marginTop", "minHeight", "className", "flexShrink", "anchor", "ModalProps", "keepMounted", "xs", "boxSizing", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/يانة موبايلات برنامج/src/components/Layout/Layout.jsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport './Layout.css';\nimport {\n  Box,\n  Drawer,\n  AppBar,\n  Toolbar,\n  List,\n  Typography,\n  Divider,\n  IconButton,\n  ListItem,\n  ListItemButton,\n  ListItemIcon,\n  ListItemText,\n  Avatar,\n  Menu,\n  MenuItem,\n  TextField,\n  InputAdornment,\n  Badge,\n  Tooltip,\n} from '@mui/material';\nimport {\n  Menu as MenuIcon,\n  Dashboard as DashboardIcon,\n  Add as AddIcon,\n  List as ListIcon,\n  People as PeopleIcon,\n  Build as BuildIcon,\n  Description as DescriptionIcon,\n  Assessment as AssessmentIcon,\n  History as HistoryIcon,\n  Settings as SettingsIcon,\n  Logout as LogoutIcon,\n  Search as SearchIcon,\n  Notifications as NotificationsIcon,\n  PhoneAndroid as PhoneIcon,\n} from '@mui/icons-material';\nimport { useNavigate, useLocation } from 'react-router-dom';\n\nconst drawerWidth = 280;\n\nconst menuItems = [\n  { text: 'لوحة التحكم', icon: <DashboardIcon />, path: '/dashboard' },\n  { text: 'إضافة صيانة', icon: <AddIcon />, path: '/add-repair' },\n  { text: 'عرض الطلبات', icon: <ListIcon />, path: '/repairs' },\n  { text: 'العملاء', icon: <PeopleIcon />, path: '/customers' },\n  { text: 'قطع الغيار', icon: <BuildIcon />, path: '/spare-parts' },\n  { text: 'المستندات', icon: <DescriptionIcon />, path: '/documents' },\n  { text: 'التقارير', icon: <AssessmentIcon />, path: '/reports' },\n  { text: 'السجل', icon: <HistoryIcon />, path: '/activity-log' },\n  { text: 'الإعدادات', icon: <SettingsIcon />, path: '/settings' },\n];\n\nconst Layout = ({ children, onLogout }) => {\n  const [mobileOpen, setMobileOpen] = useState(false);\n  const [anchorEl, setAnchorEl] = useState(null);\n  const [searchQuery, setSearchQuery] = useState('');\n  const navigate = useNavigate();\n  const location = useLocation();\n\n  const handleDrawerToggle = () => {\n    setMobileOpen(!mobileOpen);\n  };\n\n  const handleMenuClick = (event) => {\n    setAnchorEl(event.currentTarget);\n  };\n\n  const handleMenuClose = () => {\n    setAnchorEl(null);\n  };\n\n  const handleLogout = () => {\n    handleMenuClose();\n    onLogout();\n  };\n\n  const handleSearch = (e) => {\n    setSearchQuery(e.target.value);\n    // يمكن إضافة منطق البحث هنا\n  };\n\n  const drawer = (\n    <Box\n      sx={{\n        height: '100%',\n        background: 'linear-gradient(180deg, rgba(255,255,255,0.95) 0%, rgba(255,255,255,0.9) 100%)',\n        backdropFilter: 'blur(20px)',\n      }}\n    >\n      <Box\n        sx={{\n          p: 3,\n          textAlign: 'center',\n          background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n          color: 'white',\n        }}\n      >\n        <Avatar\n          sx={{\n            width: 60,\n            height: 60,\n            margin: '0 auto 12px',\n            background: 'rgba(255,255,255,0.2)',\n            backdropFilter: 'blur(10px)',\n          }}\n        >\n          <PhoneIcon sx={{ fontSize: 30 }} />\n        </Avatar>\n        <Typography variant=\"h6\" sx={{ fontWeight: 600, mb: 1 }}>\n          نظام إدارة الصيانة\n        </Typography>\n        <Typography variant=\"body2\" sx={{ opacity: 0.9 }}>\n          مركز صيانة الموبايلات\n        </Typography>\n      </Box>\n\n      <List sx={{ px: 2, py: 2 }}>\n        {menuItems.map((item, index) => (\n          <ListItem key={item.text} disablePadding sx={{ mb: 1 }}>\n            <ListItemButton\n              onClick={() => navigate(item.path)}\n              selected={location.pathname === item.path}\n              sx={{\n                borderRadius: 2,\n                transition: 'all 0.3s ease',\n                '&:hover': {\n                  background: 'linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%)',\n                  transform: 'translateX(-4px)',\n                  boxShadow: '4px 0 12px rgba(102, 126, 234, 0.2)',\n                },\n                '&.Mui-selected': {\n                  background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n                  color: 'white',\n                  '&:hover': {\n                    background: 'linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%)',\n                  },\n                  '& .MuiListItemIcon-root': {\n                    color: 'white',\n                  },\n                },\n              }}\n            >\n              <ListItemIcon\n                sx={{\n                  minWidth: 40,\n                  transition: 'all 0.3s ease',\n                }}\n              >\n                {item.icon}\n              </ListItemIcon>\n              <ListItemText\n                primary={item.text}\n                sx={{\n                  '& .MuiTypography-root': {\n                    fontWeight: location.pathname === item.path ? 600 : 400,\n                  },\n                }}\n              />\n            </ListItemButton>\n          </ListItem>\n        ))}\n      </List>\n    </Box>\n  );\n\n  return (\n    <Box sx={{ display: 'flex' }}>\n      <AppBar\n        position=\"fixed\"\n        sx={{\n          width: '100%',\n          background: 'rgba(255, 255, 255, 0.95)',\n          backdropFilter: 'blur(20px)',\n          color: 'text.primary',\n          boxShadow: '0 4px 20px rgba(0,0,0,0.1)',\n          borderBottom: '1px solid rgba(0,0,0,0.05)',\n          zIndex: 1201,\n        }}\n      >\n        <Toolbar>\n          <IconButton\n            color=\"inherit\"\n            aria-label=\"open drawer\"\n            edge=\"start\"\n            onClick={handleDrawerToggle}\n            sx={{ mr: 2, display: { sm: 'none' } }}\n          >\n            <MenuIcon />\n          </IconButton>\n\n          <TextField\n            placeholder=\"البحث في النظام...\"\n            value={searchQuery}\n            onChange={handleSearch}\n            size=\"small\"\n            sx={{\n              flexGrow: 1,\n              maxWidth: 400,\n              '& .MuiOutlinedInput-root': {\n                borderRadius: 3,\n                background: 'rgba(255,255,255,0.8)',\n                transition: 'all 0.3s ease',\n                '&:hover': {\n                  background: 'rgba(255,255,255,0.9)',\n                  transform: 'translateY(-1px)',\n                  boxShadow: '0 4px 12px rgba(0,0,0,0.1)',\n                },\n                '&.Mui-focused': {\n                  background: 'white',\n                  transform: 'translateY(-1px)',\n                  boxShadow: '0 4px 12px rgba(102, 126, 234, 0.2)',\n                },\n              },\n            }}\n            InputProps={{\n              startAdornment: (\n                <InputAdornment position=\"start\">\n                  <SearchIcon color=\"action\" />\n                </InputAdornment>\n              ),\n            }}\n          />\n\n          <Box sx={{ flexGrow: 1 }} />\n\n          <Tooltip title=\"الإشعارات\">\n            <IconButton\n              color=\"inherit\"\n              sx={{\n                mr: 2,\n                transition: 'all 0.3s ease',\n                '&:hover': {\n                  transform: 'scale(1.1)',\n                  color: 'primary.main',\n                },\n              }}\n            >\n              <Badge badgeContent={3} color=\"error\">\n                <NotificationsIcon />\n              </Badge>\n            </IconButton>\n          </Tooltip>\n\n          <Tooltip title=\"الملف الشخصي\">\n            <IconButton\n              onClick={handleMenuClick}\n              sx={{\n                transition: 'all 0.3s ease',\n                '&:hover': {\n                  transform: 'scale(1.05)',\n                },\n              }}\n            >\n              <Avatar\n                sx={{\n                  width: 40,\n                  height: 40,\n                  background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n                  fontSize: '1rem',\n                  fontWeight: 600,\n                }}\n              >\n                A\n              </Avatar>\n            </IconButton>\n          </Tooltip>\n\n          <Menu\n            anchorEl={anchorEl}\n            open={Boolean(anchorEl)}\n            onClose={handleMenuClose}\n            PaperProps={{\n              sx: {\n                borderRadius: 2,\n                boxShadow: '0 8px 32px rgba(0,0,0,0.15)',\n                border: '1px solid rgba(0,0,0,0.05)',\n              },\n            }}\n          >\n            <MenuItem onClick={() => navigate('/settings')}>\n              <ListItemIcon>\n                <SettingsIcon fontSize=\"small\" />\n              </ListItemIcon>\n              الإعدادات\n            </MenuItem>\n            <Divider />\n            <MenuItem onClick={handleLogout}>\n              <ListItemIcon>\n                <LogoutIcon fontSize=\"small\" />\n              </ListItemIcon>\n              تسجيل الخروج\n            </MenuItem>\n          </Menu>\n        </Toolbar>\n      </AppBar>\n\n      <Box\n        component=\"main\"\n        sx={{\n          flexGrow: 1,\n          p: 3,\n          width: { sm: `calc(100% - ${drawerWidth}px)` },\n          mr: { sm: 0 },\n          ml: { sm: 0 },\n          marginTop: '64px',\n          background: 'linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%)',\n          minHeight: 'calc(100vh - 64px)',\n        }}\n      >\n        <Box className=\"fade-in\">\n          {children}\n        </Box>\n      </Box>\n\n      <Box\n        component=\"nav\"\n        sx={{\n          width: { sm: drawerWidth },\n          flexShrink: { sm: 0 }\n        }}\n      >\n        <Drawer\n          variant=\"temporary\"\n          anchor=\"left\"\n          open={mobileOpen}\n          onClose={handleDrawerToggle}\n          ModalProps={{\n            keepMounted: true,\n          }}\n          sx={{\n            display: { xs: 'block', sm: 'none' },\n            '& .MuiDrawer-paper': {\n              boxSizing: 'border-box',\n              width: drawerWidth,\n              border: 'none',\n            },\n          }}\n        >\n          {drawer}\n        </Drawer>\n        <Drawer\n          variant=\"permanent\"\n          anchor=\"right\"\n          sx={{\n            display: { xs: 'none', sm: 'block' },\n            '& .MuiDrawer-paper': {\n              boxSizing: 'border-box',\n              width: drawerWidth,\n              border: 'none',\n              boxShadow: '-4px 0 20px rgba(0,0,0,0.1)',\n            },\n          }}\n          open\n        >\n          {drawer}\n        </Drawer>\n      </Box>\n    </Box>\n  );\n};\n\nexport default Layout;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,OAAO,cAAc;AACrB,SACEC,GAAG,EACHC,MAAM,EACNC,MAAM,EACNC,OAAO,EACPC,IAAI,EACJC,UAAU,EACVC,OAAO,EACPC,UAAU,EACVC,QAAQ,EACRC,cAAc,EACdC,YAAY,EACZC,YAAY,EACZC,MAAM,EACNC,IAAI,EACJC,QAAQ,EACRC,SAAS,EACTC,cAAc,EACdC,KAAK,EACLC,OAAO,QACF,eAAe;AACtB,SACEL,IAAI,IAAIM,QAAQ,EAChBC,SAAS,IAAIC,aAAa,EAC1BC,GAAG,IAAIC,OAAO,EACdnB,IAAI,IAAIoB,QAAQ,EAChBC,MAAM,IAAIC,UAAU,EACpBC,KAAK,IAAIC,SAAS,EAClBC,WAAW,IAAIC,eAAe,EAC9BC,UAAU,IAAIC,cAAc,EAC5BC,OAAO,IAAIC,WAAW,EACtBC,QAAQ,IAAIC,YAAY,EACxBC,MAAM,IAAIC,UAAU,EACpBC,MAAM,IAAIC,UAAU,EACpBC,aAAa,IAAIC,iBAAiB,EAClCC,YAAY,IAAIC,SAAS,QACpB,qBAAqB;AAC5B,SAASC,WAAW,EAAEC,WAAW,QAAQ,kBAAkB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE5D,MAAMC,WAAW,GAAG,GAAG;AAEvB,MAAMC,SAAS,GAAG,CAChB;EAAEC,IAAI,EAAE,aAAa;EAAEC,IAAI,eAAEJ,OAAA,CAAC3B,aAAa;IAAAgC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE,CAAC;EAAEC,IAAI,EAAE;AAAa,CAAC,EACpE;EAAEN,IAAI,EAAE,aAAa;EAAEC,IAAI,eAAEJ,OAAA,CAACzB,OAAO;IAAA8B,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE,CAAC;EAAEC,IAAI,EAAE;AAAc,CAAC,EAC/D;EAAEN,IAAI,EAAE,aAAa;EAAEC,IAAI,eAAEJ,OAAA,CAACxB,QAAQ;IAAA6B,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE,CAAC;EAAEC,IAAI,EAAE;AAAW,CAAC,EAC7D;EAAEN,IAAI,EAAE,SAAS;EAAEC,IAAI,eAAEJ,OAAA,CAACtB,UAAU;IAAA2B,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE,CAAC;EAAEC,IAAI,EAAE;AAAa,CAAC,EAC7D;EAAEN,IAAI,EAAE,YAAY;EAAEC,IAAI,eAAEJ,OAAA,CAACpB,SAAS;IAAAyB,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE,CAAC;EAAEC,IAAI,EAAE;AAAe,CAAC,EACjE;EAAEN,IAAI,EAAE,WAAW;EAAEC,IAAI,eAAEJ,OAAA,CAAClB,eAAe;IAAAuB,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE,CAAC;EAAEC,IAAI,EAAE;AAAa,CAAC,EACpE;EAAEN,IAAI,EAAE,UAAU;EAAEC,IAAI,eAAEJ,OAAA,CAAChB,cAAc;IAAAqB,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE,CAAC;EAAEC,IAAI,EAAE;AAAW,CAAC,EAChE;EAAEN,IAAI,EAAE,OAAO;EAAEC,IAAI,eAAEJ,OAAA,CAACd,WAAW;IAAAmB,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE,CAAC;EAAEC,IAAI,EAAE;AAAgB,CAAC,EAC/D;EAAEN,IAAI,EAAE,WAAW;EAAEC,IAAI,eAAEJ,OAAA,CAACZ,YAAY;IAAAiB,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE,CAAC;EAAEC,IAAI,EAAE;AAAY,CAAC,CACjE;AAED,MAAMC,MAAM,GAAGA,CAAC;EAAEC,QAAQ;EAAEC;AAAS,CAAC,KAAK;EAAAC,EAAA;EACzC,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAGhE,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAACiE,QAAQ,EAAEC,WAAW,CAAC,GAAGlE,QAAQ,CAAC,IAAI,CAAC;EAC9C,MAAM,CAACmE,WAAW,EAAEC,cAAc,CAAC,GAAGpE,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAMqE,QAAQ,GAAGvB,WAAW,CAAC,CAAC;EAC9B,MAAMwB,QAAQ,GAAGvB,WAAW,CAAC,CAAC;EAE9B,MAAMwB,kBAAkB,GAAGA,CAAA,KAAM;IAC/BP,aAAa,CAAC,CAACD,UAAU,CAAC;EAC5B,CAAC;EAED,MAAMS,eAAe,GAAIC,KAAK,IAAK;IACjCP,WAAW,CAACO,KAAK,CAACC,aAAa,CAAC;EAClC,CAAC;EAED,MAAMC,eAAe,GAAGA,CAAA,KAAM;IAC5BT,WAAW,CAAC,IAAI,CAAC;EACnB,CAAC;EAED,MAAMU,YAAY,GAAGA,CAAA,KAAM;IACzBD,eAAe,CAAC,CAAC;IACjBd,QAAQ,CAAC,CAAC;EACZ,CAAC;EAED,MAAMgB,YAAY,GAAIC,CAAC,IAAK;IAC1BV,cAAc,CAACU,CAAC,CAACC,MAAM,CAACC,KAAK,CAAC;IAC9B;EACF,CAAC;EAED,MAAMC,MAAM,gBACVhC,OAAA,CAAChD,GAAG;IACFiF,EAAE,EAAE;MACFC,MAAM,EAAE,MAAM;MACdC,UAAU,EAAE,gFAAgF;MAC5FC,cAAc,EAAE;IAClB,CAAE;IAAAzB,QAAA,gBAEFX,OAAA,CAAChD,GAAG;MACFiF,EAAE,EAAE;QACFI,CAAC,EAAE,CAAC;QACJC,SAAS,EAAE,QAAQ;QACnBH,UAAU,EAAE,mDAAmD;QAC/DI,KAAK,EAAE;MACT,CAAE;MAAA5B,QAAA,gBAEFX,OAAA,CAACpC,MAAM;QACLqE,EAAE,EAAE;UACFO,KAAK,EAAE,EAAE;UACTN,MAAM,EAAE,EAAE;UACVO,MAAM,EAAE,aAAa;UACrBN,UAAU,EAAE,uBAAuB;UACnCC,cAAc,EAAE;QAClB,CAAE;QAAAzB,QAAA,eAEFX,OAAA,CAACJ,SAAS;UAACqC,EAAE,EAAE;YAAES,QAAQ,EAAE;UAAG;QAAE;UAAArC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC7B,CAAC,eACTR,OAAA,CAAC3C,UAAU;QAACsF,OAAO,EAAC,IAAI;QAACV,EAAE,EAAE;UAAEW,UAAU,EAAE,GAAG;UAAEC,EAAE,EAAE;QAAE,CAAE;QAAAlC,QAAA,EAAC;MAEzD;QAAAN,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eACbR,OAAA,CAAC3C,UAAU;QAACsF,OAAO,EAAC,OAAO;QAACV,EAAE,EAAE;UAAEa,OAAO,EAAE;QAAI,CAAE;QAAAnC,QAAA,EAAC;MAElD;QAAAN,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC,eAENR,OAAA,CAAC5C,IAAI;MAAC6E,EAAE,EAAE;QAAEc,EAAE,EAAE,CAAC;QAAEC,EAAE,EAAE;MAAE,CAAE;MAAArC,QAAA,EACxBT,SAAS,CAAC+C,GAAG,CAAC,CAACC,IAAI,EAAEC,KAAK,kBACzBnD,OAAA,CAACxC,QAAQ;QAAiB4F,cAAc;QAACnB,EAAE,EAAE;UAAEY,EAAE,EAAE;QAAE,CAAE;QAAAlC,QAAA,eACrDX,OAAA,CAACvC,cAAc;UACb4F,OAAO,EAAEA,CAAA,KAAMjC,QAAQ,CAAC8B,IAAI,CAACzC,IAAI,CAAE;UACnC6C,QAAQ,EAAEjC,QAAQ,CAACkC,QAAQ,KAAKL,IAAI,CAACzC,IAAK;UAC1CwB,EAAE,EAAE;YACFuB,YAAY,EAAE,CAAC;YACfC,UAAU,EAAE,eAAe;YAC3B,SAAS,EAAE;cACTtB,UAAU,EAAE,oFAAoF;cAChGuB,SAAS,EAAE,kBAAkB;cAC7BC,SAAS,EAAE;YACb,CAAC;YACD,gBAAgB,EAAE;cAChBxB,UAAU,EAAE,mDAAmD;cAC/DI,KAAK,EAAE,OAAO;cACd,SAAS,EAAE;gBACTJ,UAAU,EAAE;cACd,CAAC;cACD,yBAAyB,EAAE;gBACzBI,KAAK,EAAE;cACT;YACF;UACF,CAAE;UAAA5B,QAAA,gBAEFX,OAAA,CAACtC,YAAY;YACXuE,EAAE,EAAE;cACF2B,QAAQ,EAAE,EAAE;cACZH,UAAU,EAAE;YACd,CAAE;YAAA9C,QAAA,EAEDuC,IAAI,CAAC9C;UAAI;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eACfR,OAAA,CAACrC,YAAY;YACXkG,OAAO,EAAEX,IAAI,CAAC/C,IAAK;YACnB8B,EAAE,EAAE;cACF,uBAAuB,EAAE;gBACvBW,UAAU,EAAEvB,QAAQ,CAACkC,QAAQ,KAAKL,IAAI,CAACzC,IAAI,GAAG,GAAG,GAAG;cACtD;YACF;UAAE;YAAAJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACY;MAAC,GAxCJ0C,IAAI,CAAC/C,IAAI;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAyCd,CACX;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACJ,CACN;EAED,oBACER,OAAA,CAAChD,GAAG;IAACiF,EAAE,EAAE;MAAE6B,OAAO,EAAE;IAAO,CAAE;IAAAnD,QAAA,gBAC3BX,OAAA,CAAC9C,MAAM;MACL6G,QAAQ,EAAC,OAAO;MAChB9B,EAAE,EAAE;QACFO,KAAK,EAAE,MAAM;QACbL,UAAU,EAAE,2BAA2B;QACvCC,cAAc,EAAE,YAAY;QAC5BG,KAAK,EAAE,cAAc;QACrBoB,SAAS,EAAE,4BAA4B;QACvCK,YAAY,EAAE,4BAA4B;QAC1CC,MAAM,EAAE;MACV,CAAE;MAAAtD,QAAA,eAEFX,OAAA,CAAC7C,OAAO;QAAAwD,QAAA,gBACNX,OAAA,CAACzC,UAAU;UACTgF,KAAK,EAAC,SAAS;UACf,cAAW,aAAa;UACxB2B,IAAI,EAAC,OAAO;UACZb,OAAO,EAAE/B,kBAAmB;UAC5BW,EAAE,EAAE;YAAEkC,EAAE,EAAE,CAAC;YAAEL,OAAO,EAAE;cAAEM,EAAE,EAAE;YAAO;UAAE,CAAE;UAAAzD,QAAA,eAEvCX,OAAA,CAAC7B,QAAQ;YAAAkC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eAEbR,OAAA,CAACjC,SAAS;UACRsG,WAAW,EAAC,qFAAoB;UAChCtC,KAAK,EAAEb,WAAY;UACnBoD,QAAQ,EAAE1C,YAAa;UACvB2C,IAAI,EAAC,OAAO;UACZtC,EAAE,EAAE;YACFuC,QAAQ,EAAE,CAAC;YACXC,QAAQ,EAAE,GAAG;YACb,0BAA0B,EAAE;cAC1BjB,YAAY,EAAE,CAAC;cACfrB,UAAU,EAAE,uBAAuB;cACnCsB,UAAU,EAAE,eAAe;cAC3B,SAAS,EAAE;gBACTtB,UAAU,EAAE,uBAAuB;gBACnCuB,SAAS,EAAE,kBAAkB;gBAC7BC,SAAS,EAAE;cACb,CAAC;cACD,eAAe,EAAE;gBACfxB,UAAU,EAAE,OAAO;gBACnBuB,SAAS,EAAE,kBAAkB;gBAC7BC,SAAS,EAAE;cACb;YACF;UACF,CAAE;UACFe,UAAU,EAAE;YACVC,cAAc,eACZ3E,OAAA,CAAChC,cAAc;cAAC+F,QAAQ,EAAC,OAAO;cAAApD,QAAA,eAC9BX,OAAA,CAACR,UAAU;gBAAC+C,KAAK,EAAC;cAAQ;gBAAAlC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACf;UAEpB;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAEFR,OAAA,CAAChD,GAAG;UAACiF,EAAE,EAAE;YAAEuC,QAAQ,EAAE;UAAE;QAAE;UAAAnE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAE5BR,OAAA,CAAC9B,OAAO;UAAC0G,KAAK,EAAC,wDAAW;UAAAjE,QAAA,eACxBX,OAAA,CAACzC,UAAU;YACTgF,KAAK,EAAC,SAAS;YACfN,EAAE,EAAE;cACFkC,EAAE,EAAE,CAAC;cACLV,UAAU,EAAE,eAAe;cAC3B,SAAS,EAAE;gBACTC,SAAS,EAAE,YAAY;gBACvBnB,KAAK,EAAE;cACT;YACF,CAAE;YAAA5B,QAAA,eAEFX,OAAA,CAAC/B,KAAK;cAAC4G,YAAY,EAAE,CAAE;cAACtC,KAAK,EAAC,OAAO;cAAA5B,QAAA,eACnCX,OAAA,CAACN,iBAAiB;gBAAAW,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChB;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,eAEVR,OAAA,CAAC9B,OAAO;UAAC0G,KAAK,EAAC,qEAAc;UAAAjE,QAAA,eAC3BX,OAAA,CAACzC,UAAU;YACT8F,OAAO,EAAE9B,eAAgB;YACzBU,EAAE,EAAE;cACFwB,UAAU,EAAE,eAAe;cAC3B,SAAS,EAAE;gBACTC,SAAS,EAAE;cACb;YACF,CAAE;YAAA/C,QAAA,eAEFX,OAAA,CAACpC,MAAM;cACLqE,EAAE,EAAE;gBACFO,KAAK,EAAE,EAAE;gBACTN,MAAM,EAAE,EAAE;gBACVC,UAAU,EAAE,mDAAmD;gBAC/DO,QAAQ,EAAE,MAAM;gBAChBE,UAAU,EAAE;cACd,CAAE;cAAAjC,QAAA,EACH;YAED;cAAAN,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,eAEVR,OAAA,CAACnC,IAAI;UACHmD,QAAQ,EAAEA,QAAS;UACnB8D,IAAI,EAAEC,OAAO,CAAC/D,QAAQ,CAAE;UACxBgE,OAAO,EAAEtD,eAAgB;UACzBuD,UAAU,EAAE;YACVhD,EAAE,EAAE;cACFuB,YAAY,EAAE,CAAC;cACfG,SAAS,EAAE,6BAA6B;cACxCuB,MAAM,EAAE;YACV;UACF,CAAE;UAAAvE,QAAA,gBAEFX,OAAA,CAAClC,QAAQ;YAACuF,OAAO,EAAEA,CAAA,KAAMjC,QAAQ,CAAC,WAAW,CAAE;YAAAT,QAAA,gBAC7CX,OAAA,CAACtC,YAAY;cAAAiD,QAAA,eACXX,OAAA,CAACZ,YAAY;gBAACsD,QAAQ,EAAC;cAAO;gBAAArC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrB,CAAC,0DAEjB;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAU,CAAC,eACXR,OAAA,CAAC1C,OAAO;YAAA+C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACXR,OAAA,CAAClC,QAAQ;YAACuF,OAAO,EAAE1B,YAAa;YAAAhB,QAAA,gBAC9BX,OAAA,CAACtC,YAAY;cAAAiD,QAAA,eACXX,OAAA,CAACV,UAAU;gBAACoD,QAAQ,EAAC;cAAO;gBAAArC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnB,CAAC,uEAEjB;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAU,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACP,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC,eAETR,OAAA,CAAChD,GAAG;MACFmI,SAAS,EAAC,MAAM;MAChBlD,EAAE,EAAE;QACFuC,QAAQ,EAAE,CAAC;QACXnC,CAAC,EAAE,CAAC;QACJG,KAAK,EAAE;UAAE4B,EAAE,EAAE,eAAenE,WAAW;QAAM,CAAC;QAC9CkE,EAAE,EAAE;UAAEC,EAAE,EAAE;QAAE,CAAC;QACbgB,EAAE,EAAE;UAAEhB,EAAE,EAAE;QAAE,CAAC;QACbiB,SAAS,EAAE,MAAM;QACjBlD,UAAU,EAAE,mDAAmD;QAC/DmD,SAAS,EAAE;MACb,CAAE;MAAA3E,QAAA,eAEFX,OAAA,CAAChD,GAAG;QAACuI,SAAS,EAAC,SAAS;QAAA5E,QAAA,EACrBA;MAAQ;QAAAN,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAENR,OAAA,CAAChD,GAAG;MACFmI,SAAS,EAAC,KAAK;MACflD,EAAE,EAAE;QACFO,KAAK,EAAE;UAAE4B,EAAE,EAAEnE;QAAY,CAAC;QAC1BuF,UAAU,EAAE;UAAEpB,EAAE,EAAE;QAAE;MACtB,CAAE;MAAAzD,QAAA,gBAEFX,OAAA,CAAC/C,MAAM;QACL0F,OAAO,EAAC,WAAW;QACnB8C,MAAM,EAAC,MAAM;QACbX,IAAI,EAAEhE,UAAW;QACjBkE,OAAO,EAAE1D,kBAAmB;QAC5BoE,UAAU,EAAE;UACVC,WAAW,EAAE;QACf,CAAE;QACF1D,EAAE,EAAE;UACF6B,OAAO,EAAE;YAAE8B,EAAE,EAAE,OAAO;YAAExB,EAAE,EAAE;UAAO,CAAC;UACpC,oBAAoB,EAAE;YACpByB,SAAS,EAAE,YAAY;YACvBrD,KAAK,EAAEvC,WAAW;YAClBiF,MAAM,EAAE;UACV;QACF,CAAE;QAAAvE,QAAA,EAEDqB;MAAM;QAAA3B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,eACTR,OAAA,CAAC/C,MAAM;QACL0F,OAAO,EAAC,WAAW;QACnB8C,MAAM,EAAC,OAAO;QACdxD,EAAE,EAAE;UACF6B,OAAO,EAAE;YAAE8B,EAAE,EAAE,MAAM;YAAExB,EAAE,EAAE;UAAQ,CAAC;UACpC,oBAAoB,EAAE;YACpByB,SAAS,EAAE,YAAY;YACvBrD,KAAK,EAAEvC,WAAW;YAClBiF,MAAM,EAAE,MAAM;YACdvB,SAAS,EAAE;UACb;QACF,CAAE;QACFmB,IAAI;QAAAnE,QAAA,EAEHqB;MAAM;QAAA3B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACK,EAAA,CAnTIH,MAAM;EAAA,QAIOb,WAAW,EACXC,WAAW;AAAA;AAAAgG,EAAA,GALxBpF,MAAM;AAqTZ,eAAeA,MAAM;AAAC,IAAAoF,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}