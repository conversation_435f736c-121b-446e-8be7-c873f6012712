{"ast": null, "code": "\"use client\";\n\nimport createSvgIcon from './utils/createSvgIcon';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nexport default createSvgIcon([/*#__PURE__*/_jsx(\"path\", {\n  d: \"M22 3H2v16h6v2h8v-2h6zm-2 14H4V5h16z\"\n}, \"0\"), /*#__PURE__*/_jsx(\"path\", {\n  d: \"M6.5 7.5H9V6H5v4h1.5zM19 12h-1.5v2.5H15V16h4z\"\n}, \"1\")], 'ScreenshotMonitorSharp');", "map": {"version": 3, "names": ["createSvgIcon", "jsx", "_jsx", "d"], "sources": ["C:/Users/<USER>/Desktop/يانة موبايلات برنامج/node_modules/@mui/icons-material/esm/ScreenshotMonitorSharp.js"], "sourcesContent": ["\"use client\";\n\nimport createSvgIcon from './utils/createSvgIcon';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nexport default createSvgIcon([/*#__PURE__*/_jsx(\"path\", {\n  d: \"M22 3H2v16h6v2h8v-2h6zm-2 14H4V5h16z\"\n}, \"0\"), /*#__PURE__*/_jsx(\"path\", {\n  d: \"M6.5 7.5H9V6H5v4h1.5zM19 12h-1.5v2.5H15V16h4z\"\n}, \"1\")], 'ScreenshotMonitorSharp');"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,aAAa,MAAM,uBAAuB;AACjD,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,eAAeF,aAAa,CAAC,CAAC,aAAaE,IAAI,CAAC,MAAM,EAAE;EACtDC,CAAC,EAAE;AACL,CAAC,EAAE,GAAG,CAAC,EAAE,aAAaD,IAAI,CAAC,MAAM,EAAE;EACjCC,CAAC,EAAE;AACL,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE,wBAAwB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}