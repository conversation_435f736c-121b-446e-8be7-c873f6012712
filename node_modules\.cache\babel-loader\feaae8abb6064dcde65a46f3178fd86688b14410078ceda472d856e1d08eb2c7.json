{"ast": null, "code": "/**\n * @mui/styled-engine v5.16.14\n *\n * @license MIT\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n'use client';\n\n/* eslint-disable no-underscore-dangle */\nimport emStyled from '@emotion/styled';\nexport default function styled(tag, options) {\n  const stylesFactory = emStyled(tag, options);\n  if (process.env.NODE_ENV !== 'production') {\n    return (...styles) => {\n      const component = typeof tag === 'string' ? `\"${tag}\"` : 'component';\n      if (styles.length === 0) {\n        console.error([`MUI: Seems like you called \\`styled(${component})()\\` without a \\`style\\` argument.`, 'You must provide a `styles` argument: `styled(\"div\")(styleYouForgotToPass)`.'].join('\\n'));\n      } else if (styles.some(style => style === undefined)) {\n        console.error(`MUI: the styled(${component})(...args) API requires all its args to be defined.`);\n      }\n      return stylesFactory(...styles);\n    };\n  }\n  return stylesFactory;\n}\n\n// eslint-disable-next-line @typescript-eslint/naming-convention\nexport const internal_processStyles = (tag, processor) => {\n  // Emotion attaches all the styles as `__emotion_styles`.\n  // Ref: https://github.com/emotion-js/emotion/blob/16d971d0da229596d6bcc39d282ba9753c9ee7cf/packages/styled/src/base.js#L186\n  if (Array.isArray(tag.__emotion_styles)) {\n    tag.__emotion_styles = processor(tag.__emotion_styles);\n  }\n};\nexport { ThemeContext, keyframes, css } from '@emotion/react';\nexport { default as StyledEngineProvider } from './StyledEngineProvider';\nexport { default as GlobalStyles } from './GlobalStyles';", "map": {"version": 3, "names": ["emStyled", "styled", "tag", "options", "stylesFactory", "process", "env", "NODE_ENV", "styles", "component", "length", "console", "error", "join", "some", "style", "undefined", "internal_processStyles", "processor", "Array", "isArray", "__emotion_styles", "ThemeContext", "keyframes", "css", "default", "StyledEngineProvider", "GlobalStyles"], "sources": ["C:/Users/<USER>/Desktop/يانة موبايلات برنامج/node_modules/@mui/styled-engine/index.js"], "sourcesContent": ["/**\n * @mui/styled-engine v5.16.14\n *\n * @license MIT\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n'use client';\n\n/* eslint-disable no-underscore-dangle */\nimport emStyled from '@emotion/styled';\nexport default function styled(tag, options) {\n  const stylesFactory = emStyled(tag, options);\n  if (process.env.NODE_ENV !== 'production') {\n    return (...styles) => {\n      const component = typeof tag === 'string' ? `\"${tag}\"` : 'component';\n      if (styles.length === 0) {\n        console.error([`MUI: Seems like you called \\`styled(${component})()\\` without a \\`style\\` argument.`, 'You must provide a `styles` argument: `styled(\"div\")(styleYouForgotToPass)`.'].join('\\n'));\n      } else if (styles.some(style => style === undefined)) {\n        console.error(`MUI: the styled(${component})(...args) API requires all its args to be defined.`);\n      }\n      return stylesFactory(...styles);\n    };\n  }\n  return stylesFactory;\n}\n\n// eslint-disable-next-line @typescript-eslint/naming-convention\nexport const internal_processStyles = (tag, processor) => {\n  // Emotion attaches all the styles as `__emotion_styles`.\n  // Ref: https://github.com/emotion-js/emotion/blob/16d971d0da229596d6bcc39d282ba9753c9ee7cf/packages/styled/src/base.js#L186\n  if (Array.isArray(tag.__emotion_styles)) {\n    tag.__emotion_styles = processor(tag.__emotion_styles);\n  }\n};\nexport { ThemeContext, keyframes, css } from '@emotion/react';\nexport { default as StyledEngineProvider } from './StyledEngineProvider';\nexport { default as GlobalStyles } from './GlobalStyles';"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,YAAY;;AAEZ;AACA,OAAOA,QAAQ,MAAM,iBAAiB;AACtC,eAAe,SAASC,MAAMA,CAACC,GAAG,EAAEC,OAAO,EAAE;EAC3C,MAAMC,aAAa,GAAGJ,QAAQ,CAACE,GAAG,EAAEC,OAAO,CAAC;EAC5C,IAAIE,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;IACzC,OAAO,CAAC,GAAGC,MAAM,KAAK;MACpB,MAAMC,SAAS,GAAG,OAAOP,GAAG,KAAK,QAAQ,GAAG,IAAIA,GAAG,GAAG,GAAG,WAAW;MACpE,IAAIM,MAAM,CAACE,MAAM,KAAK,CAAC,EAAE;QACvBC,OAAO,CAACC,KAAK,CAAC,CAAC,uCAAuCH,SAAS,qCAAqC,EAAE,8EAA8E,CAAC,CAACI,IAAI,CAAC,IAAI,CAAC,CAAC;MACnM,CAAC,MAAM,IAAIL,MAAM,CAACM,IAAI,CAACC,KAAK,IAAIA,KAAK,KAAKC,SAAS,CAAC,EAAE;QACpDL,OAAO,CAACC,KAAK,CAAC,mBAAmBH,SAAS,qDAAqD,CAAC;MAClG;MACA,OAAOL,aAAa,CAAC,GAAGI,MAAM,CAAC;IACjC,CAAC;EACH;EACA,OAAOJ,aAAa;AACtB;;AAEA;AACA,OAAO,MAAMa,sBAAsB,GAAGA,CAACf,GAAG,EAAEgB,SAAS,KAAK;EACxD;EACA;EACA,IAAIC,KAAK,CAACC,OAAO,CAAClB,GAAG,CAACmB,gBAAgB,CAAC,EAAE;IACvCnB,GAAG,CAACmB,gBAAgB,GAAGH,SAAS,CAAChB,GAAG,CAACmB,gBAAgB,CAAC;EACxD;AACF,CAAC;AACD,SAASC,YAAY,EAAEC,SAAS,EAAEC,GAAG,QAAQ,gBAAgB;AAC7D,SAASC,OAAO,IAAIC,oBAAoB,QAAQ,wBAAwB;AACxE,SAASD,OAAO,IAAIE,YAAY,QAAQ,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}