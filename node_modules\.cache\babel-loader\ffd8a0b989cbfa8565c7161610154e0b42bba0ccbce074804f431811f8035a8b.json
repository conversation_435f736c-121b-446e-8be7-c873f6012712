{"ast": null, "code": "import * as React from 'react';\nexport function prepareForSlot(Component) {\n  return /*#__PURE__*/React.forwardRef(function Slot(props, ref) {\n    const {\n      ownerState,\n      ...other\n    } = props;\n    return /*#__PURE__*/React.createElement(Component, {\n      ...other,\n      ref\n    });\n  });\n}", "map": {"version": 3, "names": ["React", "prepareForSlot", "Component", "forwardRef", "Slot", "props", "ref", "ownerState", "other", "createElement"], "sources": ["C:/Users/<USER>/Desktop/يانة موبايلات برنامج/node_modules/@mui/base/utils/prepareForSlot.js"], "sourcesContent": ["import * as React from 'react';\nexport function prepareForSlot(Component) {\n  return /*#__PURE__*/React.forwardRef(function Slot(props, ref) {\n    const {\n      ownerState,\n      ...other\n    } = props;\n    return /*#__PURE__*/React.createElement(Component, {\n      ...other,\n      ref\n    });\n  });\n}"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,OAAO,SAASC,cAAcA,CAACC,SAAS,EAAE;EACxC,OAAO,aAAaF,KAAK,CAACG,UAAU,CAAC,SAASC,IAAIA,CAACC,KAAK,EAAEC,GAAG,EAAE;IAC7D,MAAM;MACJC,UAAU;MACV,GAAGC;IACL,CAAC,GAAGH,KAAK;IACT,OAAO,aAAaL,KAAK,CAACS,aAAa,CAACP,SAAS,EAAE;MACjD,GAAGM,KAAK;MACRF;IACF,CAAC,CAAC;EACJ,CAAC,CAAC;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}