/* إصلاح مشاكل التخطيط */

/* التأكد من أن الـ Drawer لا يظهر فوق المحتوى */
.MuiDrawer-root {
  z-index: 1200 !important;
}

.MuiDrawer-paper {
  z-index: 1200 !important;
}

/* التأكد من أن الـ AppBar يظهر فوق الـ Drawer */
.MuiAppBar-root {
  z-index: 1300 !important;
}

/* ضمان ملء المحتوى للجهة اليسرى */
.layout-container {
  position: relative !important;
  width: 100% !important;
  height: 100vh !important;
}

.main-content * {
  max-width: 100% !important;
}

/* التأكد من أن المحتوى الرئيسي يظهر في المكان الصحيح */
.main-content {
  margin-top: 64px !important;
  margin-left: 0 !important;
  margin-right: 0 !important;
  position: absolute !important;
  left: 0 !important;
  right: 0 !important;
  width: 100% !important;
  z-index: 1 !important;
}

@media (min-width: 600px) {
  .main-content {
    margin-left: 0 !important;
    margin-right: 280px !important;
    width: calc(100% - 280px) !important;
    left: 0 !important;
    right: 280px !important;
  }
}

/* إصلاح مشاكل التداخل */
.layout-container {
  display: flex;
  min-height: 100vh;
  position: relative;
  overflow-x: hidden;
}

/* تحسين الـ Drawer - في الجهة اليمنى */
.sidebar-drawer {
  position: fixed !important;
  top: 0;
  right: 0;
  height: 100vh;
  z-index: 1200 !important;
}

/* تحسين الـ AppBar */
.top-appbar {
  position: fixed !important;
  top: 0;
  left: 0;
  right: 0;
  width: 100% !important;
  z-index: 1300 !important;
}

/* إصلاح مشاكل الـ RTL - اللوحة الجانبية في اليمين */
[dir="rtl"] .main-content {
  margin-right: 0 !important;
  margin-left: 0 !important;
}

@media (min-width: 600px) {
  [dir="rtl"] .main-content {
    margin-right: 280px !important;
    margin-left: 0 !important;
    width: calc(100% - 280px) !important;
  }
}

[dir="rtl"] .top-appbar {
  left: 0 !important;
  right: 0 !important;
  width: 100% !important;
}

/* تحسين الانتقالات */
.layout-transition {
  transition: all 0.3s ease-in-out;
}

/* إصلاح مشاكل التمرير */
.main-content {
  overflow-y: auto;
  overflow-x: hidden;
  max-height: calc(100vh - 64px);
}

/* تحسين الاستجابة للموبايل */
@media (max-width: 599px) {
  .main-content {
    margin-left: 0 !important;
    margin-right: 0 !important;
    width: 100% !important;
    left: 0 !important;
    right: 0 !important;
  }

  .top-appbar {
    width: 100% !important;
    margin-left: 0 !important;
    margin-right: 0 !important;
  }
}

/* ضمان توسيع المحتوى بالكامل */
.main-content {
  box-sizing: border-box !important;
}

.main-content > * {
  width: 100% !important;
  box-sizing: border-box !important;
}

/* إزالة أي margins أو paddings غير مرغوبة */
.layout-container .main-content {
  padding-left: 24px !important;
  padding-right: 24px !important;
}
